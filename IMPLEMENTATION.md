# Assistant-Go Web Implementation Guide

This guide provides detailed implementation instructions for building Assistant-Go as a web application using Go's standard library, Material Design 3, and Kubernetes deployment.

## Frontend Implementation

### Material Design 3 with Tailwind CSS

The frontend uses server-side rendering with Material Design 3 principles implemented through Tailwind CSS. The design system emphasizes clarity, usability, and a modern aesthetic with blue and white as primary colors.

#### Color System

```css
/* tailwind.config.js */
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#1976D2', // Material Blue 700
          light: '#42A5F5',   // Material Blue 400
          dark: '#1565C0',    // Material Blue 800
        },
        surface: {
          light: '#FFFFFF',
          dark: '#1E1E1E',
        },
        background: {
          light: '#FAFAFA',
          dark: '#121212',
        },
        error: '#B71C1C',
        warning: '#F57C00',
        success: '#1B5E20',
      }
    }
  },
  darkMode: 'class', // Enable dark mode with class strategy
}
```

#### Base Template Structure

```html
<!-- web/templates/base.html -->
<!DOCTYPE html>
<html lang="en" class="{{if eq .Theme "dark"}}dark{{end}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - Assistant-Go</title>
    
    <!-- Embedded CSS -->
    <style>{{.CSS}}</style>
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{.CSRFToken}}">
</head>
<body class="bg-background-light dark:bg-background-dark text-gray-900 dark:text-gray-100">
    <!-- Navigation -->
    <nav class="bg-primary dark:bg-primary-dark shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-8">
                    <h1 class="text-xl font-bold text-white">Assistant-Go</h1>
                    {{template "nav-items" .}}
                </div>
                <div class="flex items-center space-x-4">
                    {{template "user-menu" .}}
                    <button id="theme-toggle" class="text-white hover:bg-primary-light p-2 rounded">
                        <span class="material-icons">{{if eq .Theme "dark"}}light_mode{{else}}dark_mode{{end}}</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        {{template "content" .}}
    </main>
    
    <!-- WebSocket connection for real-time updates -->
    <script>
        const ws = new WebSocket('ws://{{.Host}}/ws');
        ws.onmessage = function(event) {
            handleRealtimeUpdate(JSON.parse(event.data));
        };
    </script>
</body>
</html>
```

### Component Templates with templ UI

Using templ UI patterns for consistent Material Design components:

```go
// web/templates/components/button.templ
package components

templ Button(text string, variant string, attrs map[string]string) {
    <button 
        class={buttonClasses(variant)}
        { attrs... }
    >
        { text }
    </button>
}

func buttonClasses(variant string) string {
    base := "px-4 py-2 rounded font-medium transition-colors duration-200 "
    
    switch variant {
    case "primary":
        return base + "bg-primary text-white hover:bg-primary-dark active:bg-primary-darker"
    case "secondary":
        return base + "border border-primary text-primary hover:bg-primary/10"
    case "text":
        return base + "text-primary hover:bg-primary/10"
    default:
        return base + "bg-gray-200 text-gray-800 hover:bg-gray-300"
    }
}

// Usage in templates
templ DashboardActions() {
    <div class="flex gap-2">
        @Button("Create", "primary", map[string]string{"id": "create-btn"})
        @Button("Cancel", "text", map[string]string{"id": "cancel-btn"})
    </div>
}
```

### Real-time UI Updates

Implementing partial page updates without full refreshes:

```javascript
// web/static/js/realtime.js
class RealtimeUpdater {
    constructor() {
        this.ws = null;
        this.reconnectInterval = 5000;
        this.connect();
    }
    
    connect() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        this.ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.authenticate();
        };
        
        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleUpdate(data);
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected, reconnecting...');
            setTimeout(() => this.connect(), this.reconnectInterval);
        };
    }
    
    authenticate() {
        const token = localStorage.getItem('jwt_token');
        this.ws.send(JSON.stringify({
            type: 'auth',
            token: token
        }));
    }
    
    handleUpdate(data) {
        switch(data.type) {
            case 'container_stats':
                this.updateContainerStats(data.payload);
                break;
            case 'log_line':
                this.appendLogLine(data.payload);
                break;
            case 'k8s_event':
                this.showK8sEvent(data.payload);
                break;
        }
    }
    
    updateContainerStats(stats) {
        // Update CPU/Memory charts without page refresh
        const cpuBar = document.querySelector(`#cpu-${stats.container_id}`);
        if (cpuBar) {
            cpuBar.style.width = `${stats.cpu_percent}%`;
            cpuBar.textContent = `${stats.cpu_percent}%`;
        }
    }
    
    appendLogLine(log) {
        const logContainer = document.querySelector('#log-output');
        if (logContainer) {
            const line = document.createElement('div');
            line.className = 'log-line';
            line.textContent = `[${log.timestamp}] ${log.message}`;
            logContainer.appendChild(line);
            
            // Auto-scroll to bottom
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    new RealtimeUpdater();
});
```

## Module Implementation Details

### Docker Module

The Docker module provides container management with real-time monitoring:

```go
// internal/api/handlers/docker.go
package handlers

type DockerHandler struct {
    docker   *docker.Client
    logger   *slog.Logger
    template *template.Template
}

func (h *DockerHandler) ListContainers(w http.ResponseWriter, r *http.Request) {
    containers, err := h.docker.ContainerList(r.Context(), types.ContainerListOptions{
        All: true,
    })
    if err != nil {
        h.renderError(w, r, err)
        return
    }
    
    // Transform to view model
    viewContainers := make([]ContainerView, len(containers))
    for i, c := range containers {
        viewContainers[i] = ContainerView{
            ID:      c.ID[:12],
            Name:    strings.TrimPrefix(c.Names[0], "/"),
            Image:   c.Image,
            Status:  c.Status,
            State:   c.State,
            Created: time.Unix(c.Created, 0),
            Ports:   formatPorts(c.Ports),
        }
    }
    
    h.renderTemplate(w, r, "docker/containers", PageData{
        Title: "Docker Containers",
        Content: map[string]interface{}{
            "Containers": viewContainers,
        },
    })
}

func (h *DockerHandler) StreamContainerStats(w http.ResponseWriter, r *http.Request) {
    containerID := chi.URLParam(r, "id")
    
    // Upgrade to WebSocket
    conn, err := websocket.Upgrade(w, r, nil, 1024, 1024)
    if err != nil {
        return
    }
    defer conn.Close()
    
    // Stream stats
    stats, err := h.docker.ContainerStats(r.Context(), containerID, true)
    if err != nil {
        conn.WriteJSON(map[string]string{"error": err.Error()})
        return
    }
    defer stats.Body.Close()
    
    decoder := json.NewDecoder(stats.Body)
    for {
        var stat types.StatsJSON
        if err := decoder.Decode(&stat); err != nil {
            break
        }
        
        // Calculate percentages
        cpuPercent := calculateCPUPercent(&stat)
        memoryPercent := calculateMemoryPercent(&stat)
        
        // Send to client
        conn.WriteJSON(map[string]interface{}{
            "type": "container_stats",
            "payload": map[string]interface{}{
                "container_id":   containerID,
                "cpu_percent":    cpuPercent,
                "memory_percent": memoryPercent,
                "memory_usage":   stat.MemoryStats.Usage,
                "memory_limit":   stat.MemoryStats.Limit,
            },
        })
        
        time.Sleep(1 * time.Second)
    }
}
```

### PostgreSQL Module with sqlc

Database queries are type-safe using sqlc:

```sql
-- migrations/queries/queries.sql
-- name: GetUserByEmail :one
SELECT * FROM users WHERE email = $1;

-- name: CreateWorkspace :one
INSERT INTO workspaces (name, owner_id)
VALUES ($1, $2)
RETURNING *;

-- name: GetWorkspaceMembers :many
SELECT u.* FROM users u
JOIN workspace_members wm ON u.id = wm.user_id
WHERE wm.workspace_id = $1;

-- name: SaveQuery :one
INSERT INTO saved_queries (workspace_id, name, query, created_by)
VALUES ($1, $2, $3, $4)
RETURNING *;
```

Generated Go code usage:

```go
// internal/storage/postgres/queries.go
type Queries struct {
    db DBTX
}

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (User, error) {
    row := q.db.QueryRowContext(ctx, getUserByEmail, email)
    var i User
    err := row.Scan(
        &i.ID,
        &i.Email,
        &i.Name,
        &i.PasswordHash,
        &i.Role,
        &i.ThemePreference,
        &i.CreatedAt,
        &i.UpdatedAt,
    )
    return i, err
}

// Handler usage
func (h *PostgresHandler) ExecuteQuery(w http.ResponseWriter, r *http.Request) {
    var req QueryRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request", http.StatusBadRequest)
        return
    }
    
    // Validate query
    if err := h.validateQuery(req.Query); err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }
    
    // Execute with timeout
    ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
    defer cancel()
    
    rows, err := h.db.QueryContext(ctx, req.Query)
    if err != nil {
        h.renderQueryError(w, r, err)
        return
    }
    defer rows.Close()
    
    // Convert to result format
    result := h.rowsToResult(rows)
    
    // Save to history if requested
    if req.SaveToHistory {
        claims := r.Context().Value("claims").(Claims)
        _, err = h.queries.SaveQuery(ctx, SaveQueryParams{
            WorkspaceID: claims.WorkspaceID,
            Name:        req.Name,
            Query:       req.Query,
            CreatedBy:   claims.UserID,
        })
    }
    
    h.renderJSON(w, result)
}
```

### Kubernetes Integration

The Kubernetes module uses client-go for cluster operations:

```go
// internal/core/k8s/client.go
type K8sClient struct {
    clientset  kubernetes.Interface
    restConfig *rest.Config
    logger     *slog.Logger
}

func NewK8sClient(kubeconfig string) (*K8sClient, error) {
    config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
    if err != nil {
        // Try in-cluster config
        config, err = rest.InClusterConfig()
        if err != nil {
            return nil, err
        }
    }
    
    clientset, err := kubernetes.NewForConfig(config)
    if err != nil {
        return nil, err
    }
    
    return &K8sClient{
        clientset:  clientset,
        restConfig: config,
        logger:     slog.Default(),
    }, nil
}

// Watch resources with WebSocket streaming
func (c *K8sClient) WatchPods(ctx context.Context, namespace string, output chan<- PodEvent) error {
    watcher, err := c.clientset.CoreV1().Pods(namespace).Watch(ctx, metav1.ListOptions{})
    if err != nil {
        return err
    }
    defer watcher.Stop()
    
    for event := range watcher.ResultChan() {
        pod, ok := event.Object.(*v1.Pod)
        if !ok {
            continue
        }
        
        output <- PodEvent{
            Type: string(event.Type),
            Pod: PodInfo{
                Name:      pod.Name,
                Namespace: pod.Namespace,
                Status:    string(pod.Status.Phase),
                Ready:     c.isPodReady(pod),
                Age:       time.Since(pod.CreationTimestamp.Time),
            },
        }
    }
    
    return nil
}
```

### Search Integration with SearXNG

```go
// internal/core/search/searxng.go
type SearXNGClient struct {
    baseURL string
    client  *http.Client
    logger  *slog.Logger
}

func (s *SearXNGClient) Search(ctx context.Context, query string, options SearchOptions) (*SearchResults, error) {
    params := url.Values{
        "q":        {query},
        "format":   {"json"},
        "language": {options.Language},
    }
    
    if len(options.Categories) > 0 {
        params.Set("categories", strings.Join(options.Categories, ","))
    }
    
    req, err := http.NewRequestWithContext(ctx, "GET", 
        s.baseURL+"/search?"+params.Encode(), nil)
    if err != nil {
        return nil, err
    }
    
    resp, err := s.client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var results SearchResults
    if err := json.NewDecoder(resp.Body).Decode(&results); err != nil {
        return nil, err
    }
    
    // Enhance results with AI summaries if enabled
    if options.EnableAISummary {
        results.Summary = s.generateSummary(ctx, results)
    }
    
    return &results, nil
}
```

## Development Environment Setup

### Local Development with Air

Configure hot reload for rapid development:

```toml
# .air.toml
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  bin = "./tmp/main"
  cmd = "go build -o ./tmp/main ./cmd/api-server"
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "deployments"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = "0s"
  log = "build-errors.log"
  send_interrupt = false
  stop_on_error = true

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = false

[misc]
  clean_on_exit = false
```

### Database Migrations

Use golang-migrate for schema management:

```go
// cmd/migrate/main.go
package main

import (
    "database/sql"
    "flag"
    "log"
    "os"
    
    "github.com/golang-migrate/migrate/v4"
    "github.com/golang-migrate/migrate/v4/database/postgres"
    _ "github.com/golang-migrate/migrate/v4/source/file"
)

func main() {
    var direction string
    flag.StringVar(&direction, "direction", "up", "Migration direction (up/down)")
    flag.Parse()
    
    db, err := sql.Open("postgres", os.Getenv("DATABASE_URL"))
    if err != nil {
        log.Fatal(err)
    }
    
    driver, err := postgres.WithInstance(db, &postgres.Config{})
    if err != nil {
        log.Fatal(err)
    }
    
    m, err := migrate.NewWithDatabaseInstance(
        "file://migrations",
        "postgres", driver)
    if err != nil {
        log.Fatal(err)
    }
    
    switch direction {
    case "up":
        err = m.Up()
    case "down":
        err = m.Down()
    default:
        log.Fatal("Invalid direction")
    }
    
    if err != nil && err != migrate.ErrNoChange {
        log.Fatal(err)
    }
    
    log.Println("Migration completed successfully")
}
```

## Testing Strategy

### Unit Testing

```go
// internal/api/handlers/docker_test.go
func TestDockerHandler_ListContainers(t *testing.T) {
    // Mock Docker client
    mockDocker := &MockDockerClient{
        containers: []types.Container{
            {
                ID:     "abc123",
                Names:  []string{"/test-container"},
                Image:  "nginx:latest",
                State:  "running",
                Status: "Up 2 hours",
            },
        },
    }
    
    handler := &DockerHandler{
        docker:   mockDocker,
        logger:   slog.Default(),
        template: loadTestTemplates(t),
    }
    
    req := httptest.NewRequest("GET", "/api/docker/containers", nil)
    rec := httptest.NewRecorder()
    
    handler.ListContainers(rec, req)
    
    assert.Equal(t, http.StatusOK, rec.Code)
    assert.Contains(t, rec.Body.String(), "test-container")
    assert.Contains(t, rec.Body.String(), "nginx:latest")
}
```

### Integration Testing

```go
// internal/api/integration_test.go
func TestFullStackIntegration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration test")
    }
    
    // Start test database
    db := setupTestDatabase(t)
    defer db.Close()
    
    // Start test server
    srv := setupTestServer(t, db)
    defer srv.Close()
    
    // Test user flow
    client := &http.Client{}
    
    // 1. Register user
    registerResp := registerUser(t, client, srv.URL)
    assert.Equal(t, http.StatusCreated, registerResp.StatusCode)
    
    // 2. Login
    loginResp := login(t, client, srv.URL)
    token := extractToken(t, loginResp)
    
    // 3. Create workspace
    workspaceResp := createWorkspace(t, client, srv.URL, token)
    assert.Equal(t, http.StatusCreated, workspaceResp.StatusCode)
    
    // 4. Execute query
    queryResp := executeQuery(t, client, srv.URL, token)
    assert.Equal(t, http.StatusOK, queryResp.StatusCode)
}
```

## Production Deployment

### Building Production Images

```dockerfile
# Dockerfile
FROM golang:1.24-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/api-server

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/web ./web

EXPOSE 8080
CMD ["./main"]
```

### Kubernetes Production Configuration

```yaml
# deployments/overlays/production/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-server
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    spec:
      containers:
      - name: api-server
        image: assistant-go:latest
        env:
        - name: GO_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

This implementation guide provides a comprehensive foundation for building Assistant-Go as a modern web application. The architecture emphasizes simplicity, performance, and maintainability while leveraging Go's strengths and Kubernetes' scalability.